#!/usr/bin/env python3
"""
Multi-Agent Workflow Visualizer

A Streamlit-based tool for visualizing and analyzing LLM-powered multi-agent 
collaboration workflows from JSON log files.

Requirements:
- streamlit
- plotly  
- pandas
- numpy

Installation:
    conda run -n ada pip install streamlit plotly pandas numpy

Usage:
    conda run -n ada streamlit run workflow_visualizer.py

Features:
- Interactive Timeline: Visualize chronological flow of agent interactions
- Agent Analysis: See which agents are most active and their interaction patterns  
- Token Usage Tracking: Monitor prompt and completion token usage across calls
- Call Details: Drill down into individual LLM calls to see messages and responses
- Multiple Log Support: Easily switch between different log files via sidebar

Author: GitHub Copilot
Date: July 27, 2025
"""

import streamlit as st
import json
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timezone
import os
import re
from pathlib import Path

# Configure Streamlit page
st.set_page_config(
    page_title="Multi-Agent Workflow Visualizer",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

def extract_agent_name(agent_id):
    """Extract clean agent name from agent_id string"""
    if not agent_id:
        return "Unknown"
    
    # Extract the main agent name before the first underscore or slash
    if "/" in agent_id:
        agent_name = agent_id.split("/")[0]
    else:
        agent_name = agent_id
    
    # Clean up common patterns
    if "_" in agent_name:
        parts = agent_name.split("_")
        if len(parts) > 1:
            agent_name = parts[0]
    
    # Remove common suffixes and make it more readable
    agent_name = agent_name.replace("MagenticOne", "")
    agent_name = agent_name.replace("Orchestrator", "Orchestrator")
    
    return agent_name if agent_name else "Unknown"

def parse_timestamp(timestamp_str):
    """Parse timestamp string with timezone handling"""
    try:
        # Handle different timestamp formats
        if "," in timestamp_str:
            # Format: "2025-07-27 03:26:42,413"
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
        elif "T" in timestamp_str:
            # ISO format
            dt = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
        else:
            # Try standard format without microseconds
            dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
        
        return dt
    except Exception as e:
        # Return a default datetime if parsing fails
        print(f"Warning: Error parsing timestamp {timestamp_str}: {e}")
        return datetime.now()

def parse_response_from_log(log_file_path, call_index):
    """Parse response content from raw log file for a specific call"""
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for LLM call entries with response data
        import re
        
        # Find all LLM call entries (each line that contains "type": "LLMCall")
        llm_call_lines = []
        for line in content.split('\n'):
            if '"type": "LLMCall"' in line:
                llm_call_lines.append(line)
        
        if len(llm_call_lines) >= call_index:
            line = llm_call_lines[call_index - 1]  # call_index is 1-based
            try:
                # Extract the JSON part from the log line (after the timestamp)
                json_start = line.find('{"')
                if json_start != -1:
                    json_str = line[json_start:]
                    call_data = json.loads(json_str)
                    
                    # Extract response content
                    response = call_data.get('response', {})
                    if 'choices' in response and response['choices']:
                        choice = response['choices'][0]
                        if 'message' in choice and 'content' in choice['message']:
                            return choice['message']['content']
            except Exception as e:
                print(f"Error parsing call {call_index}: {e}")
                pass
    except Exception as e:
        print(f"Error reading log file: {e}")
        pass
    
    return ""

def load_log_file(file_path):
    """Load and parse a JSON log file with enhanced response data"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Try to find corresponding raw log file for response data
        log_file_path = None
        if "formatted" in str(file_path):
            # Try to find the corresponding .log file
            base_name = str(file_path).replace("_formatted.json", ".log")
            if os.path.exists(base_name):
                log_file_path = base_name
        
        # Add response data from log file if available
        if log_file_path and 'llm_calls' in data:
            for call in data['llm_calls']:
                call_index = call.get('call_index', 0)
                if call_index > 0:
                    response_content = parse_response_from_log(log_file_path, call_index)
                    if response_content:
                        # Store the response content in the call data
                        if 'response' not in call:
                            call['response'] = {}
                        if 'choices' not in call['response']:
                            call['response']['choices'] = [{'message': {'content': response_content}}]
                        elif not call['response']['choices']:
                            call['response']['choices'] = [{'message': {'content': response_content}}]
                        elif 'message' not in call['response']['choices'][0]:
                            call['response']['choices'][0]['message'] = {'content': response_content}
                        elif 'content' not in call['response']['choices'][0]['message']:
                            call['response']['choices'][0]['message']['content'] = response_content
        
        return data
    except Exception as e:
        st.error(f"Error loading file {file_path}: {e}")
        return None

def create_timeline_chart(df):
    """Create an interactive timeline chart showing agent interactions"""
    
    # Create figure
    fig = go.Figure()
    
    # Better color mapping for different agents with high contrast
    agents = df['agent_name'].unique()
    
    # Define a custom color palette with good contrast
    custom_colors = [
        '#1f77b4',  # Blue - Orchestrator
        '#ff7f0e',  # Orange - WebSurfer  
        '#2ca02c',  # Green - Coder
        '#d62728',  # Red - ComputerTerminal
        '#9467bd',  # Purple - FileSurfer
        '#8c564b',  # Brown
        '#e377c2',  # Pink
        '#7f7f7f',  # Gray
        '#bcbd22',  # Olive
        '#17becf'   # Cyan
    ]
    
    agent_colors = {}
    for i, agent in enumerate(agents):
        agent_colors[agent] = custom_colors[i % len(custom_colors)]
    
    # Add traces for each agent
    for i, agent in enumerate(agents):
        agent_data = df[df['agent_name'] == agent]
        
        fig.add_trace(go.Scatter(
            x=agent_data['timestamp'],
            y=[i] * len(agent_data),
            mode='markers+lines',
            name=agent,
            marker=dict(
                size=12,
                color=agent_colors[agent],
                line=dict(width=2, color='white')
            ),
            line=dict(color=agent_colors[agent], width=3),
            text=agent_data.apply(lambda row: 
                f"Call {row['call_index']}<br>"
                f"Agent: {row['agent_name']}<br>"
                f"Time: {row['timestamp']}<br>"
                f"Prompt Tokens: {row['prompt_tokens']}<br>"
                f"Completion Tokens: {row['completion_tokens']}<br>"
                f"Duration: {row['duration']:.2f}s", axis=1),
            hovertemplate='%{text}<extra></extra>',
            customdata=agent_data['call_index'].values
        ))
    
    # Update layout
    fig.update_layout(
        title="Multi-Agent Workflow Timeline",
        xaxis_title="Timeline",
        yaxis_title="Agents",
        yaxis=dict(
            tickmode='array',
            tickvals=list(range(len(agents))),
            ticktext=agents
        ),
        height=max(400, len(agents) * 80),
        showlegend=True,
        hovermode='closest'
    )
    
    return fig

def create_metrics_dashboard(df, metadata):
    """Create metrics dashboard"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Calls", len(df))
    
    with col2:
        total_duration = df['duration'].sum()
        st.metric("Total Duration", f"{total_duration:.2f}s")
    
    with col3:
        if metadata:
            st.metric("Total Tokens", f"{metadata.get('total_tokens', 'N/A'):,}")
        else:
            st.metric("Total Tokens", f"{df['prompt_tokens'].sum() + df['completion_tokens'].sum():,}")
    
    with col4:
        unique_agents = df['agent_name'].nunique()
        st.metric("Active Agents", unique_agents)

def create_token_usage_chart(df):
    """Create token usage visualization"""
    fig = go.Figure()
    
    fig.add_trace(go.Bar(
        x=df['call_index'],
        y=df['prompt_tokens'],
        name='Prompt Tokens',
        marker_color='#1f77b4'  # Blue
    ))
    
    fig.add_trace(go.Bar(
        x=df['call_index'],
        y=df['completion_tokens'],
        name='Completion Tokens',
        marker_color='#ff7f0e'  # Orange
    ))
    
    fig.update_layout(
        title="Token Usage per Call",
        xaxis_title="Call Index",
        yaxis_title="Number of Tokens",
        barmode='stack'
    )
    
    return fig

def create_agent_interaction_chart(df):
    """Create agent interaction flow chart"""
    # Create a simple flow showing the sequence of agent calls
    agent_sequence = df['agent_name'].tolist()
    
    # Count transitions between agents
    transitions = {}
    for i in range(len(agent_sequence) - 1):
        current = agent_sequence[i]
        next_agent = agent_sequence[i + 1]
        key = f"{current} → {next_agent}"
        transitions[key] = transitions.get(key, 0) + 1
    
    if transitions:
        transition_df = pd.DataFrame([
            {'Transition': k, 'Count': v} for k, v in transitions.items()
        ])
        
        fig = px.bar(
            transition_df, 
            x='Transition', 
            y='Count',
            title="Agent Interaction Patterns"
        )
        fig.update_xaxes(tickangle=45)
        return fig
    
    return None

def display_call_details(df, call_index):
    """Display detailed information for a specific call"""
    call_data = df[df['call_index'] == call_index].iloc[0]
    
    st.subheader(f"Call {call_index} Details")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write(f"**Agent:** {call_data['agent_name']}")
        st.write(f"**Timestamp:** {call_data['timestamp']}")
        st.write(f"**Duration:** {call_data['duration']:.2f}s")
    
    with col2:
        st.write(f"**Prompt Tokens:** {call_data['prompt_tokens']}")
        st.write(f"**Completion Tokens:** {call_data['completion_tokens']}")
        st.write(f"**Model:** {call_data.get('model', 'N/A')}")
    
    # Display input messages if available
    if 'input_messages' in call_data and call_data['input_messages']:
        st.subheader("Input Messages")
        try:
            input_messages = json.loads(call_data['input_messages']) if isinstance(call_data['input_messages'], str) else call_data['input_messages']
            for i, msg in enumerate(input_messages):
                with st.expander(f"Message {i+1} - {msg.get('role', 'unknown')}", expanded=True):
                    content = msg.get('content', '')
                    if len(content) > 500:
                        st.text_area("Content", content, height=200, key=f"input_msg_{call_index}_{i}")
                    else:
                        st.text(content)
        except Exception as e:
            st.error(f"Error displaying input messages: {e}")
    
    # Display LLM response if available
    if 'response_content' in call_data and call_data['response_content']:
        st.subheader("LLM Response")
        response_content = call_data['response_content']
        if len(response_content) > 500:
            st.text_area("Response Content", response_content, height=200, key=f"response_{call_index}")
        else:
            st.text(response_content)

def main():
    st.title("🤖 Multi-Agent Workflow Visualizer")
    st.markdown("Analyze and visualize LLM-powered multi-agent collaboration workflows")
    
    # Sidebar for file selection
    st.sidebar.header("Configuration")
    
    # Get available log files (only LLM calls files)
    logs_dir = Path("../logs/generated")
    if logs_dir.exists():
        all_files = [f for f in logs_dir.glob("*.json") if "formatted" in f.name]
        
        # Filter to only include files with llm_calls data
        log_files = []
        for file_path in all_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if 'llm_calls' in data and data['llm_calls']:
                    log_files.append(file_path)
            except:
                continue  # Skip files that can't be parsed
        
        if log_files:
            selected_file = st.sidebar.selectbox(
                "Select Log File:",
                options=log_files,
                format_func=lambda x: x.name
            )
            
            # Show file info
            try:
                with open(selected_file, 'r', encoding='utf-8') as f:
                    preview_data = json.load(f)
                call_count = len(preview_data.get('llm_calls', []))
                st.sidebar.info(f"📊 {call_count} LLM calls in this file")
            except:
                pass
            
            if st.sidebar.button("Load File"):
                # Load and process the selected file
                data = load_log_file(selected_file)
                
                if data:
                    st.session_state['log_data'] = data
                    st.session_state['selected_file'] = selected_file.name
                    st.success(f"Loaded {selected_file.name}")
        else:
            st.sidebar.warning("No compatible LLM call log files found in logs/generated/")
            st.sidebar.info("Looking for files with 'llm_calls' data structure")
    else:
        st.sidebar.error("Logs directory not found")
    
    # Main content
    if 'log_data' in st.session_state:
        data = st.session_state['log_data']
        
        st.header(f"Analysis: {st.session_state['selected_file']}")
        
        # Extract metadata if available
        metadata = data.get('metadata', {})
        llm_calls = data.get('llm_calls', [])
        
        if not llm_calls:
            st.error("No LLM calls found in the log file")
            return
        
        # Process the data
        processed_data = []
        prev_timestamp = None
        
        for i, call in enumerate(llm_calls):
            try:
                timestamp = parse_timestamp(call['timestamp'])
                agent_name = extract_agent_name(call.get('agent_id', ''))
                
                # Calculate duration between calls
                duration = 0
                if i > 0 and prev_timestamp:
                    duration = (timestamp - prev_timestamp).total_seconds()
                
                # Handle input messages safely
                input_messages = call.get('input_messages', [])
                if isinstance(input_messages, str):
                    try:
                        input_messages = json.loads(input_messages)
                    except:
                        input_messages = []
                
                # Handle response data safely
                response_data = call.get('response', {})
                response_content = ""
                try:
                    # Extract response content from choices[0].message.content
                    if 'choices' in response_data and response_data['choices']:
                        choice = response_data['choices'][0]
                        if 'message' in choice and 'content' in choice['message']:
                            response_content = choice['message']['content']
                except Exception:
                    response_content = ""
                
                processed_data.append({
                    'call_index': call['call_index'],
                    'timestamp': timestamp,
                    'agent_name': agent_name,
                    'agent_id': call.get('agent_id', ''),
                    'prompt_tokens': call.get('prompt_tokens', 0),
                    'completion_tokens': call.get('completion_tokens', 0),
                    'duration': duration,
                    'model': call.get('response', {}).get('model', 'N/A'),
                    'input_messages': input_messages,
                    'response_content': response_content
                })
                
                prev_timestamp = timestamp
                
            except Exception as e:
                st.warning(f"Skipping call {call.get('call_index', 'unknown')} due to error: {e}")
                continue
        
        if not processed_data:
            st.error("No valid data could be processed from the log file")
            return
        
        df = pd.DataFrame(processed_data)
        
        # Display metadata
        if metadata:
            st.subheader("Workflow Metadata")
            col1, col2, col3 = st.columns(3)
            with col1:
                st.write(f"**Source File:** {metadata.get('source_file', 'N/A')}")
                st.write(f"**Total Calls:** {metadata.get('total_calls', 'N/A')}")
            with col2:
                st.write(f"**First Call:** {metadata.get('first_call_timestamp', 'N/A')}")
                st.write(f"**Last Call:** {metadata.get('last_call_timestamp', 'N/A')}")
            with col3:
                st.write(f"**Total Tokens:** {metadata.get('total_tokens', 'N/A'):,}")
                st.write(f"**Prompt Tokens:** {metadata.get('total_prompt_tokens', 'N/A'):,}")
        
        # Metrics dashboard
        st.subheader("Overview Metrics")
        create_metrics_dashboard(df, metadata)
        
        # Timeline visualization
        st.subheader("Workflow Timeline")
        timeline_fig = create_timeline_chart(df)
        selected_points = st.plotly_chart(timeline_fig, use_container_width=True, key="timeline")
        
        # Additional visualizations
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Token Usage")
            token_fig = create_token_usage_chart(df)
            st.plotly_chart(token_fig, use_container_width=True)
        
        with col2:
            st.subheader("Agent Activity")
            agent_counts = df['agent_name'].value_counts()
            agent_fig = px.pie(
                values=agent_counts.values,
                names=agent_counts.index,
                title="Calls per Agent"
            )
            st.plotly_chart(agent_fig, use_container_width=True)
        
        # Agent interaction patterns
        interaction_fig = create_agent_interaction_chart(df)
        if interaction_fig:
            st.subheader("Agent Interaction Patterns")
            st.plotly_chart(interaction_fig, use_container_width=True)
        
        # Call details section
        st.subheader("Call Details")
        selected_call = st.selectbox(
            "Select a call to view details:",
            options=df['call_index'].tolist(),
            format_func=lambda x: f"Call {x} - {df[df['call_index']==x]['agent_name'].iloc[0]}"
        )
        
        if selected_call:
            display_call_details(df, selected_call)
        
        # Raw data view
        if st.checkbox("Show Raw Data"):
            st.subheader("Raw Data")
            st.dataframe(df)
    
    else:
        st.info("Please select and load a log file from the sidebar to begin analysis.")
        
        # Show available compatible files as a preview
        logs_dir = Path("../logs/generated")
        if logs_dir.exists():
            all_files = [f for f in logs_dir.glob("*.json") if "formatted" in f.name]
            
            # Filter to only include files with llm_calls data
            compatible_files = []
            for file_path in all_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    if 'llm_calls' in data and data['llm_calls']:
                        call_count = len(data['llm_calls'])
                        compatible_files.append((file_path.name, call_count))
                except:
                    continue
            
            if compatible_files:
                st.subheader("Available LLM Call Log Files:")
                for filename, call_count in compatible_files:
                    st.write(f"📄 **{filename}** - {call_count} calls")
            else:
                st.warning("No compatible log files found with LLM calls data.")

if __name__ == "__main__":
    main()

# File Structure After Setup:
# Multi_Agent/
# ├── scripts/
# │   ├── workflow_visualizer.py     # Main Streamlit application (this file)
# │   ├── test_visualizer.py         # Test script for validation
# │   ├── launch_visualizer.sh       # Launch script with auto-setup
# │   └── utils.py                   # Other project utilities
# └── logs/generated/                # Log files directory
#     ├── scenario_1_*_llm_calls_formatted.json  # Compatible files
#     └── scenario_1_*_log_formatted.json        # Ignored files
#
# Quick Start:
# 1. cd scripts
# 2. chmod +x launch_visualizer.sh
# 3. ./launch_visualizer.sh
# 4. Open http://localhost:8501 in browser
# 5. Select log file from sidebar and click "Load File"
